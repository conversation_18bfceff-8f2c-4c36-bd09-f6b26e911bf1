<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knead & Nourish - Coming Soon</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="coming-soon">
                <h2>Coming Soon</h2>              
            </div>
            <div class="logo-container">
                <!-- Replace this with your actual logo -->
                <img src="logo.png" alt="Knead & Nourish Logo" class="logo" id="logo">
                <!-- Fallback text logo if image doesn't load -->
                <div class="text-logo" id="textLogo">
                    <h1>Knead & Nourish</h1>
                </div>
                <p>We're baking up something special for you!</p>
            </div>

        </div>
    </div>

    <!-- Wheat stalks container - stalks will be generated dynamically based on screen width -->
    <div class="wheat-field" id="wheatField">
    </div>

    <script>
        // Show text logo if image fails to load
        function showTextLogo() {
            const logoImg = document.getElementById('logo');
            const textLogo = document.getElementById('textLogo');

            if (logoImg) {
                logoImg.style.display = 'none';
            }
            if (textLogo) {
                textLogo.style.display = 'block';
            }
        }

        // Set up the error handler
        document.addEventListener('DOMContentLoaded', function() {
            const logoImg = document.getElementById('logo');
            if (logoImg) {
                logoImg.onerror = showTextLogo;

                // Also check if the image is already broken (in case the error fired before we set the handler)
                if (logoImg.complete && logoImg.naturalWidth === 0) {
                    showTextLogo();
                }
            }
        });

        // Dynamic wheat field generation and animation
        let mouseX = window.innerWidth / 2; // Start at center
        let wheatStalks = [];
        let stalkData = []; // Store stalk properties
        let gustSystems = [
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 0 },
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 1 },
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 2 }
        ];
        let gustFrequency = {
            baseInterval: 2000, // Base 2 seconds
            variationRange: 4000, // +0 to +4 seconds variation
            lastGustTime: 0,
            minStagger: 800 // Minimum 800ms between gust starts
        };

        // Generate wheat stalks based on screen width
        function generateWheatStalks() {
            const wheatField = document.getElementById('wheatField');
            const windowWidth = window.innerWidth;

            // Calculate number of stalks based on screen width (tripled for denser field)
            let stalkCount;
            if (windowWidth >= 1920) {
                stalkCount = 450; // Ultra-wide screens (150 x 3)
            } else if (windowWidth >= 1440) {
                stalkCount = 360; // Large screens (120 x 3)
            } else if (windowWidth >= 1024) {
                stalkCount = 300; // Desktop (100 x 3)
            } else if (windowWidth >= 768) {
                stalkCount = 240; // Tablet (80 x 3)
            } else if (windowWidth >= 480) {
                stalkCount = 180; // Mobile (60 x 3)
            } else {
                stalkCount = 120; // Small mobile (40 x 3)
            }

            // Clear existing stalks
            wheatField.innerHTML = '';
            wheatStalks = [];
            stalkData = [];

            // Generate height variations
            const heights = [68, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95];

            // Create stalks with four-segment structure for realistic bending
            for (let i = 0; i < stalkCount; i++) {
                const stalk = document.createElement('div');
                stalk.className = 'wheat-stalk';
                stalk.setAttribute('data-index', i);

                // Assign random height
                const randomHeight = heights[Math.floor(Math.random() * heights.length)];
                stalk.style.height = randomHeight + 'px';

                // Create four segments for progressive bending
                const baseSegment = document.createElement('div');
                baseSegment.className = 'wheat-segment wheat-base';

                const lowerMiddleSegment = document.createElement('div');
                lowerMiddleSegment.className = 'wheat-segment wheat-lower-middle';

                const upperMiddleSegment = document.createElement('div');
                upperMiddleSegment.className = 'wheat-segment wheat-upper-middle';

                const topSegment = document.createElement('div');
                topSegment.className = 'wheat-segment wheat-top';

                // Calculate segment heights (base: 30%, lower-middle: 25%, upper-middle: 25%, top: 20%)
                const baseHeight = Math.floor(randomHeight * 0.3);
                const lowerMiddleHeight = Math.floor(randomHeight * 0.25);
                const upperMiddleHeight = Math.floor(randomHeight * 0.25);
                const topHeight = randomHeight - baseHeight - lowerMiddleHeight - upperMiddleHeight;

                // Set heights and positions for each segment
                baseSegment.style.height = baseHeight + 'px';
                baseSegment.style.bottom = '0px';

                lowerMiddleSegment.style.height = lowerMiddleHeight + 'px';
                lowerMiddleSegment.style.bottom = baseHeight + 'px';

                upperMiddleSegment.style.height = upperMiddleHeight + 'px';
                upperMiddleSegment.style.bottom = (baseHeight + lowerMiddleHeight) + 'px';

                topSegment.style.height = topHeight + 'px';
                topSegment.style.bottom = (baseHeight + lowerMiddleHeight + upperMiddleHeight) + 'px';

                // Set transform origins for chained movement
                // Base segment pivots from its bottom center (stalk root)
                baseSegment.style.transformOrigin = 'bottom center';

                // Middle and top segments will pivot from their bottom center since we're using translate to position them
                lowerMiddleSegment.style.transformOrigin = 'bottom center';
                upperMiddleSegment.style.transformOrigin = 'bottom center';
                topSegment.style.transformOrigin = 'bottom center';

                // Add all segments as siblings to the stalk container
                stalk.appendChild(baseSegment);
                stalk.appendChild(lowerMiddleSegment);
                stalk.appendChild(upperMiddleSegment);
                stalk.appendChild(topSegment);

                // Store stalk data for wind calculations
                const stalkInfo = {
                    element: stalk,
                    baseSegment: baseSegment,
                    lowerMiddleSegment: lowerMiddleSegment,
                    upperMiddleSegment: upperMiddleSegment,
                    topSegment: topSegment,
                    height: randomHeight,
                    baseHeight: baseHeight,
                    lowerMiddleHeight: lowerMiddleHeight,
                    upperMiddleHeight: upperMiddleHeight,
                    topHeight: topHeight,
                    flexibility: randomHeight / 95, // Taller stalks are more flexible (0.7 to 1.0)
                    naturalSway: Math.random() * 0.3 + 0.8, // Individual character (0.8 to 1.1)
                    position: i / stalkCount // Position across screen (0 to 1)
                };

                wheatField.appendChild(stalk);
                wheatStalks.push(stalk);
                stalkData.push(stalkInfo);
            }
        }

        // Mouse tracking
        document.addEventListener('mousemove', function(e) {
            mouseX = e.clientX;
        });

        // Touch tracking for mobile devices
        let isTouch = false;

        document.addEventListener('touchstart', function(e) {
            isTouch = true;
            if (e.touches.length > 0) {
                mouseX = e.touches[0].clientX;
            }
            // Prevent default to avoid scrolling and other touch behaviors
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('touchmove', function(e) {
            if (e.touches.length > 0) {
                mouseX = e.touches[0].clientX;
            }
            // Prevent default to avoid scrolling and other touch behaviors
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('touchend', function(e) {
            // Return to center/neutral position when finger is lifted
            mouseX = window.innerWidth / 2;
            // Prevent default to avoid unwanted click events
            e.preventDefault();
        }, { passive: false });

        // Reset touch flag when mouse is used (for hybrid devices)
        document.addEventListener('mouseenter', function() {
            isTouch = false;
        });

        // Enhanced gust system management with multiple simultaneous gusts
        function updateGustSystems() {
            const currentTime = Date.now();

            // Check each gust system
            gustSystems.forEach(gust => {
                // Try to start a new gust if this one is inactive and it's time
                if (!gust.active && currentTime >= gust.nextGustTime) {
                    // Check if enough time has passed since the last gust started (stagger requirement)
                    if (currentTime - gustFrequency.lastGustTime >= gustFrequency.minStagger) {
                        // Start a new gust with varied characteristics
                        gust.active = true;

                        // Vary direction (60% left-to-right, 40% right-to-left for natural feel)
                        const startFromLeft = Math.random() < 0.6;
                        gust.direction = startFromLeft ? 1 : -1;
                        gust.position = startFromLeft ? -25 : 125; // Start further off-screen

                        // Vary strength significantly (0.2 to 1.2 for dramatic range)
                        gust.strength = Math.random() * 1.0 + 0.2;

                        // Vary width based on strength (stronger gusts are wider)
                        const baseWidth = 15 + (gust.strength * 20); // 15-35 stalks
                        gust.width = baseWidth + (Math.random() * 10 - 5); // ±5 variation

                        // Speed correlates with strength (stronger = faster)
                        const baseSpeed = 0.8 + (gust.strength * 2.2); // 1.0 to 3.0
                        gust.speed = baseSpeed + (Math.random() * 0.8 - 0.4); // ±0.4 variation

                        // Set next gust time with high variation
                        const nextInterval = gustFrequency.baseInterval + (Math.random() * gustFrequency.variationRange);
                        gust.nextGustTime = currentTime + nextInterval;

                        // Update last gust start time for staggering
                        gustFrequency.lastGustTime = currentTime;
                    }
                }

                // Update active gusts
                if (gust.active) {
                    // Move the gust across the screen
                    gust.position += gust.direction * gust.speed;

                    // Check if gust has passed through completely
                    const exitThreshold = gust.direction > 0 ? 125 : -25;
                    if ((gust.direction > 0 && gust.position > exitThreshold) ||
                        (gust.direction < 0 && gust.position < exitThreshold)) {
                        gust.active = false;
                    }
                }
            });
        }

        function updateWheatAnimation() {
            const windowWidth = window.innerWidth;
            const centerZone = windowWidth * 0.1; // 10% center zone
            const centerX = windowWidth / 2;

            // Calculate mouse wind effect
            const distanceFromCenter = Math.abs(mouseX - centerX);
            let mouseWindStrength = 0;
            let mouseDirection = 0;

            if (distanceFromCenter > centerZone / 2) {
                mouseDirection = mouseX > centerX ? 1 : -1;
                const maxDistance = (windowWidth / 2) - (centerZone / 2);
                mouseWindStrength = Math.min((distanceFromCenter - centerZone / 2) / maxDistance, 1);
            }

            // Update all gust systems
            updateGustSystems();

            // Apply wind effects to each stalk with progressive bending
            stalkData.forEach((stalkInfo, index) => {
                let totalRotation = 0;

                // Mouse wind effect
                if (mouseWindStrength > 0) {
                    const mouseRotation = mouseDirection * mouseWindStrength * 25 * stalkInfo.flexibility * stalkInfo.naturalSway;
                    totalRotation += mouseRotation;
                }

                // Multiple gust wind effects (can stack)
                gustSystems.forEach(gust => {
                    if (gust.active) {
                        const stalkPosition = stalkInfo.position * 100; // Convert to percentage
                        const distanceFromGust = Math.abs(stalkPosition - gust.position);

                        if (distanceFromGust <= gust.width) {
                            // Stalk is within gust range
                            const gustInfluence = Math.max(0, 1 - (distanceFromGust / gust.width));

                            // Enhanced gust effect with strength variation
                            const baseGustRotation = 35; // Increased base rotation for more dramatic effect
                            const strengthMultiplier = Math.pow(gust.strength, 1.5); // Non-linear strength scaling
                            const gustRotation = gust.direction * strengthMultiplier * baseGustRotation * gustInfluence * stalkInfo.flexibility * stalkInfo.naturalSway;

                            totalRotation += gustRotation;
                        }
                    }
                });

                // Add subtle natural sway even without wind
                const naturalSway = Math.sin(Date.now() * 0.001 + index * 0.1) * 2 * stalkInfo.naturalSway;
                totalRotation += naturalSway;

                // Calculate progressive bending for realistic physics with chained movement (4 segments)
                const baseRotation = totalRotation * 0.2;         // Base: 20% of total rotation
                const lowerMiddleRotation = totalRotation * 0.4;   // Lower-middle: 40% of total rotation
                const upperMiddleRotation = totalRotation * 0.7;   // Upper-middle: 70% of total rotation
                const topRotation = totalRotation;                 // Top: 100% of total rotation

                // Apply dynamic transition based on wind strength
                const windIntensity = Math.abs(totalRotation);
                const transitionSpeed = Math.max(0.1, Math.min(0.5, 0.3 - (windIntensity * 0.005))); // Faster transitions for stronger winds

                // Calculate chained positions for realistic connected movement (4 segments)
                // Base segment rotates from stalk root
                stalkInfo.baseSegment.style.transform = `rotate(${baseRotation}deg)`;
                stalkInfo.baseSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the base segment ends up after rotation
                const baseRadians = (baseRotation * Math.PI) / 180;
                const baseEndX = Math.sin(baseRadians) * stalkInfo.baseHeight;
                const baseEndY = Math.cos(baseRadians) * stalkInfo.baseHeight;

                // Position lower-middle segment to start from the end of the base segment
                const lowerMiddleOriginalY = stalkInfo.baseHeight;
                const lowerMiddleTranslateX = baseEndX;
                const lowerMiddleTranslateY = lowerMiddleOriginalY - baseEndY;

                stalkInfo.lowerMiddleSegment.style.transform = `translate(${lowerMiddleTranslateX}px, ${lowerMiddleTranslateY}px) rotate(${lowerMiddleRotation}deg)`;
                stalkInfo.lowerMiddleSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the lower-middle segment ends up after its rotation and translation
                const lowerMiddleRadians = (lowerMiddleRotation * Math.PI) / 180;
                const lowerMiddleEndX = lowerMiddleTranslateX + Math.sin(lowerMiddleRadians) * stalkInfo.lowerMiddleHeight;
                const lowerMiddleEndY = baseEndY + Math.cos(lowerMiddleRadians) * stalkInfo.lowerMiddleHeight;

                // Position upper-middle segment to start from the end of the lower-middle segment
                const upperMiddleOriginalY = stalkInfo.baseHeight + stalkInfo.lowerMiddleHeight;
                const upperMiddleTranslateX = lowerMiddleEndX;
                const upperMiddleTranslateY = upperMiddleOriginalY - lowerMiddleEndY;

                stalkInfo.upperMiddleSegment.style.transform = `translate(${upperMiddleTranslateX}px, ${upperMiddleTranslateY}px) rotate(${upperMiddleRotation}deg)`;
                stalkInfo.upperMiddleSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the upper-middle segment ends up after its rotation and translation
                const upperMiddleRadians = (upperMiddleRotation * Math.PI) / 180;
                const upperMiddleEndX = upperMiddleTranslateX + Math.sin(upperMiddleRadians) * stalkInfo.upperMiddleHeight;
                const upperMiddleEndY = lowerMiddleEndY + Math.cos(upperMiddleRadians) * stalkInfo.upperMiddleHeight;

                // Position top segment to start from the end of the upper-middle segment
                const topOriginalY = stalkInfo.baseHeight + stalkInfo.lowerMiddleHeight + stalkInfo.upperMiddleHeight;
                const topTranslateX = upperMiddleEndX;
                const topTranslateY = topOriginalY - upperMiddleEndY;

                stalkInfo.topSegment.style.transform = `translate(${topTranslateX}px, ${topTranslateY}px) rotate(${topRotation}deg)`;
                stalkInfo.topSegment.style.transition = `transform ${transitionSpeed}s ease-out`;
            });
        }

        // Animation loop for continuous updates
        function animationLoop() {
            updateWheatAnimation();
            requestAnimationFrame(animationLoop);
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            generateWheatStalks();
            mouseX = window.innerWidth / 2; // Reset mouse position
            // Reset all gust systems
            gustSystems.forEach((gust, index) => {
                gust.active = false;
                gust.nextGustTime = Date.now() + 2000 + (index * gustFrequency.minStagger);
            });
            gustFrequency.lastGustTime = 0;
        });

        // Initialize wheat field
        document.addEventListener('DOMContentLoaded', function() {
            generateWheatStalks();
            // Set initial gust timing with staggered starts
            const currentTime = Date.now();
            gustSystems.forEach((gust, index) => {
                const baseDelay = Math.random() * 4000 + 2000; // 2-6 seconds initial delay
                gust.nextGustTime = currentTime + baseDelay + (index * gustFrequency.minStagger);
            });
            gustFrequency.lastGustTime = currentTime;
            // Start animation loop
            animationLoop();
        });
    </script>
</body>
</html>
