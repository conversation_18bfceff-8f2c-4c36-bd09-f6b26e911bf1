/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    background: linear-gradient(135deg, #f5f1eb 0%, #e8ddd4 100%);
    min-height: 100vh;
    overflow: hidden;
}

/* Container for centering content */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.content {
    text-align: center;
    max-width: 600px;
    width: 100%;
}

/* Logo styles */
.logo-container {
    margin-bottom: 40px;
}

.logo {
    max-width: 300px;
    max-height: 200px;
    width: auto;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* Text logo fallback */
.text-logo {
    display: none; /* Hidden by default */
    animation: fadeInUp 0.5s ease-out;
}

.text-logo h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 700;
    color: #8b4513;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    margin-top: 0;
}

/* Coming soon text */
.coming-soon h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 400;
    color: #5d4037;
    margin-bottom: 20px;
    letter-spacing: 2px;
    animation: fadeInUp 1s ease-out;
}

.coming-soon p {
    font-size: 1.2rem;
    color: #6d4c41;
    font-weight: 300;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.logo-container p {
    font-size: 1.2rem;
    color: #6d4c41;
    font-weight: 300;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 20px;
    }
    
    .logo {
        max-width: 250px;
        max-height: 150px;
    }
    
    .text-logo h1 {
        font-size: 2.5rem;
    }
    
    .coming-soon h2 {
        font-size: 2rem;
        letter-spacing: 1px;
    }
    
    .coming-soon p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .logo {
        max-width: 200px;
        max-height: 120px;
    }
    
    .text-logo h1 {
        font-size: 2rem;
    }
    
    .coming-soon h2 {
        font-size: 1.5rem;
    }
    
    .coming-soon p {
        font-size: 0.9rem;
    }
}

/* Add a subtle background pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Wheat stalks styles */
.wheat-field {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 120px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0 0.2%;
    pointer-events: none;
    z-index: 10;
    gap: 0.1px;
}

.wheat-stalk {
    width: 2px;
    height: 80px;
    position: relative;
    opacity: 0.9;
}

/* Wheat segment base styles */
.wheat-segment {
    width: 100%;
    position: absolute;
    bottom: 0;
    transform-origin: bottom center;
    transition: transform 0.3s ease-out;
}

/* Base segment (bottom 30%) - darkest colors, positioned at bottom */
.wheat-base {
    background: linear-gradient(to top,
        #4a4a2a 0%,     /* Dark brown/green base */
        #5a5030 100%    /* Slightly lighter brown */
    );
    border-radius: 1px 1px 0 0;
    z-index: 1;
    transform-origin: bottom center; /* Pivots from stalk root */
}

/* Lower-middle segment (25%) - medium-dark colors, positioned above base */
.wheat-lower-middle {
    background: linear-gradient(to top,
        #5a5030 0%,     /* Slightly lighter brown */
        #6b5b3d 100%    /* Medium brown */
    );
    border-radius: 0;
    z-index: 2;
    transform-origin: bottom center; /* Will be adjusted to pivot from top of base segment */
}

/* Upper-middle segment (25%) - medium-light colors, positioned above lower-middle */
.wheat-upper-middle {
    background: linear-gradient(to top,
        #6b5b3d 0%,     /* Medium brown */
        #8b7355 50%,    /* Light brown */
        #a68b5b 100%    /* Tan */
    );
    border-radius: 0;
    z-index: 3;
    transform-origin: bottom center; /* Will be adjusted to pivot from top of lower-middle segment */
}

/* Top segment (top 20%) - lightest colors, contains berries, positioned at top */
.wheat-top {
    background: linear-gradient(to top,
        #a68b5b 0%,     /* Tan */
        #c4a373 50%,    /* Light tan */
        #d4b896 100%    /* Very light tan top */
    );
    border-radius: 0 0 1px 1px;
    z-index: 4;
    transform-origin: bottom center; /* Will be adjusted to pivot from top of upper-middle segment */
}

/* Create realistic grass blade effect on all segments */
.wheat-segment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 60%,
        rgba(255, 255, 255, 0.3) 100%
    );
    border-radius: inherit;
}

/* Add multiple berries/seeds only to the top segment */
.wheat-top::after {
    content: '';
    position: absolute;
    top: 20%;
    left: -0.5px;
    width: 1px;
    height: 1px;
    background: #8b6914;
    border-radius: 50%;
    box-shadow:
        /* Left side berries - distributed throughout top segment */
        -1px 3px 0 0.5px #8b6914,
        -0.5px 6px 0 0.3px #a0751a,
        -1px 9px 0 0.4px #8b6914,
        -0.5px 12px 0 0.2px #a0751a,
        -1px 15px 0 0.5px #8b6914,
        -0.5px 18px 0 0.3px #a0751a,

        /* Right side berries - distributed throughout top segment */
        1px 2px 0 0.4px #a0751a,
        0.5px 5px 0 0.3px #8b6914,
        1px 8px 0 0.5px #a0751a,
        0.5px 11px 0 0.2px #8b6914,
        1px 14px 0 0.4px #a0751a,
        0.5px 17px 0 0.3px #8b6914,

        /* Center berries - distributed throughout top segment */
        0px 4px 0 0.2px #8b6914,
        0px 7px 0 0.3px #a0751a,
        0px 10px 0 0.2px #8b6914,
        0px 13px 0 0.4px #a0751a,
        0px 16px 0 0.2px #8b6914,
        0px 19px 0 0.3px #a0751a;
}

/* Vary the height and appearance of different stalks - creating natural field variation */
.wheat-stalk[data-index="1"] { height: 75px; }
.wheat-stalk[data-index="2"] { height: 85px; }
.wheat-stalk[data-index="3"] { height: 70px; }
.wheat-stalk[data-index="4"] { height: 90px; }
.wheat-stalk[data-index="5"] { height: 78px; }
.wheat-stalk[data-index="6"] { height: 82px; }
.wheat-stalk[data-index="7"] { height: 88px; }
.wheat-stalk[data-index="8"] { height: 73px; }
.wheat-stalk[data-index="9"] { height: 86px; }
.wheat-stalk[data-index="10"] { height: 79px; }
.wheat-stalk[data-index="11"] { height: 84px; }
.wheat-stalk[data-index="12"] { height: 77px; }
.wheat-stalk[data-index="13"] { height: 91px; }
.wheat-stalk[data-index="14"] { height: 74px; }
.wheat-stalk[data-index="15"] { height: 87px; }
.wheat-stalk[data-index="16"] { height: 81px; }
.wheat-stalk[data-index="17"] { height: 76px; }
.wheat-stalk[data-index="18"] { height: 89px; }
.wheat-stalk[data-index="19"] { height: 83px; }
.wheat-stalk[data-index="20"] { height: 72px; }
.wheat-stalk[data-index="21"] { height: 94px; }
.wheat-stalk[data-index="22"] { height: 68px; }
.wheat-stalk[data-index="23"] { height: 85px; }
.wheat-stalk[data-index="24"] { height: 92px; }
.wheat-stalk[data-index="25"] { height: 71px; }
.wheat-stalk[data-index="26"] { height: 88px; }
.wheat-stalk[data-index="27"] { height: 75px; }
.wheat-stalk[data-index="28"] { height: 93px; }
.wheat-stalk[data-index="29"] { height: 69px; }
.wheat-stalk[data-index="30"] { height: 86px; }
.wheat-stalk[data-index="31"] { height: 78px; }
.wheat-stalk[data-index="32"] { height: 90px; }
.wheat-stalk[data-index="33"] { height: 74px; }
.wheat-stalk[data-index="34"] { height: 87px; }
.wheat-stalk[data-index="35"] { height: 82px; }
.wheat-stalk[data-index="36"] { height: 76px; }
.wheat-stalk[data-index="37"] { height: 91px; }
.wheat-stalk[data-index="38"] { height: 73px; }
.wheat-stalk[data-index="39"] { height: 89px; }
.wheat-stalk[data-index="40"] { height: 77px; }
.wheat-stalk[data-index="41"] { height: 84px; }
.wheat-stalk[data-index="42"] { height: 95px; }
.wheat-stalk[data-index="43"] { height: 70px; }
.wheat-stalk[data-index="44"] { height: 88px; }
.wheat-stalk[data-index="45"] { height: 81px; }
.wheat-stalk[data-index="46"] { height: 75px; }
.wheat-stalk[data-index="47"] { height: 92px; }
.wheat-stalk[data-index="48"] { height: 79px; }
.wheat-stalk[data-index="49"] { height: 85px; }
.wheat-stalk[data-index="50"] { height: 72px; }
.wheat-stalk[data-index="51"] { height: 89px; }
.wheat-stalk[data-index="52"] { height: 76px; }
.wheat-stalk[data-index="53"] { height: 93px; }
.wheat-stalk[data-index="54"] { height: 68px; }
.wheat-stalk[data-index="55"] { height: 87px; }
.wheat-stalk[data-index="56"] { height: 80px; }
.wheat-stalk[data-index="57"] { height: 91px; }
.wheat-stalk[data-index="58"] { height: 74px; }
.wheat-stalk[data-index="59"] { height: 86px; }
.wheat-stalk[data-index="60"] { height: 78px; }
.wheat-stalk[data-index="61"] { height: 94px; }
.wheat-stalk[data-index="62"] { height: 71px; }
.wheat-stalk[data-index="63"] { height: 88px; }
.wheat-stalk[data-index="64"] { height: 83px; }
.wheat-stalk[data-index="65"] { height: 75px; }
.wheat-stalk[data-index="66"] { height: 90px; }
.wheat-stalk[data-index="67"] { height: 77px; }
.wheat-stalk[data-index="68"] { height: 85px; }
.wheat-stalk[data-index="69"] { height: 92px; }
.wheat-stalk[data-index="70"] { height: 69px; }
.wheat-stalk[data-index="71"] { height: 87px; }
.wheat-stalk[data-index="72"] { height: 81px; }
.wheat-stalk[data-index="73"] { height: 73px; }
.wheat-stalk[data-index="74"] { height: 95px; }
.wheat-stalk[data-index="75"] { height: 79px; }
.wheat-stalk[data-index="76"] { height: 84px; }
.wheat-stalk[data-index="77"] { height: 88px; }
.wheat-stalk[data-index="78"] { height: 72px; }
.wheat-stalk[data-index="79"] { height: 91px; }
.wheat-stalk[data-index="80"] { height: 76px; }
.wheat-stalk[data-index="81"] { height: 89px; }
.wheat-stalk[data-index="82"] { height: 82px; }
.wheat-stalk[data-index="83"] { height: 70px; }
.wheat-stalk[data-index="84"] { height: 93px; }
.wheat-stalk[data-index="85"] { height: 78px; }
.wheat-stalk[data-index="86"] { height: 86px; }
.wheat-stalk[data-index="87"] { height: 74px; }
.wheat-stalk[data-index="88"] { height: 90px; }
.wheat-stalk[data-index="89"] { height: 85px; }
.wheat-stalk[data-index="90"] { height: 77px; }
.wheat-stalk[data-index="91"] { height: 92px; }
.wheat-stalk[data-index="92"] { height: 71px; }
.wheat-stalk[data-index="93"] { height: 87px; }
.wheat-stalk[data-index="94"] { height: 83px; }
.wheat-stalk[data-index="95"] { height: 75px; }
.wheat-stalk[data-index="96"] { height: 94px; }
.wheat-stalk[data-index="97"] { height: 80px; }
.wheat-stalk[data-index="98"] { height: 88px; }
.wheat-stalk[data-index="99"] { height: 81px; }

/* Responsive wheat stalks */
@media (max-width: 768px) {
    .wheat-field {
        height: 80px;
        padding: 0 0.1%;
        gap: 0.05px;
    }

    .wheat-stalk {
        width: 0.8px;
        height: 60px;
    }

    .wheat-top::after {
        width: 0.8px;
        height: 0.8px;
        top: 25%;
        box-shadow:
            /* Scaled down berries for mobile - top segment only */
            -0.6px 2px 0 0.3px #8b6914,
            -0.3px 4px 0 0.2px #a0751a,
            -0.6px 6px 0 0.3px #8b6914,
            -0.3px 8px 0 0.2px #a0751a,
            -0.6px 10px 0 0.3px #8b6914,
            -0.3px 12px 0 0.2px #a0751a,

            0.6px 1px 0 0.2px #a0751a,
            0.3px 3px 0 0.2px #8b6914,
            0.6px 5px 0 0.3px #a0751a,
            0.3px 7px 0 0.2px #8b6914,
            0.6px 9px 0 0.2px #a0751a,
            0.3px 11px 0 0.2px #8b6914,

            0px 2px 0 0.2px #8b6914,
            0px 4px 0 0.2px #a0751a,
            0px 6px 0 0.2px #8b6914,
            0px 8px 0 0.2px #a0751a,
            0px 10px 0 0.2px #8b6914;
    }

    /* Adjust heights for mobile */
    .wheat-stalk[data-index="1"] { height: 55px; }
    .wheat-stalk[data-index="2"] { height: 65px; }
    .wheat-stalk[data-index="3"] { height: 50px; }
    .wheat-stalk[data-index="4"] { height: 70px; }
    .wheat-stalk[data-index="5"] { height: 58px; }
    .wheat-stalk[data-index="6"] { height: 62px; }
    .wheat-stalk[data-index="7"] { height: 68px; }
    .wheat-stalk[data-index="8"] { height: 53px; }
    .wheat-stalk[data-index="9"] { height: 66px; }
    .wheat-stalk[data-index="10"] { height: 59px; }
    .wheat-stalk[data-index="11"] { height: 64px; }
    .wheat-stalk[data-index="12"] { height: 57px; }
    .wheat-stalk[data-index="13"] { height: 71px; }
    .wheat-stalk[data-index="14"] { height: 54px; }
    .wheat-stalk[data-index="15"] { height: 67px; }
    .wheat-stalk[data-index="16"] { height: 61px; }
    .wheat-stalk[data-index="17"] { height: 56px; }
    .wheat-stalk[data-index="18"] { height: 69px; }
    .wheat-stalk[data-index="19"] { height: 63px; }
}

@media (max-width: 480px) {
    .wheat-field {
        height: 60px;
        padding: 0 0.05%;
        gap: 0.02px;
    }

    .wheat-stalk {
        width: 0.6px;
        height: 45px;
    }

    .wheat-top::after {
        width: 0.5px;
        height: 0.5px;
        top: 30%;
        box-shadow:
            /* Very small berries for small mobile - top segment only */
            -0.4px 1px 0 0.2px #8b6914,
            -0.2px 3px 0 0.1px #a0751a,
            -0.4px 5px 0 0.2px #8b6914,
            -0.2px 7px 0 0.1px #a0751a,
            -0.4px 9px 0 0.2px #8b6914,

            0.4px 1px 0 0.1px #a0751a,
            0.2px 3px 0 0.1px #8b6914,
            0.4px 5px 0 0.2px #a0751a,
            0.2px 7px 0 0.1px #8b6914,
            0.4px 9px 0 0.1px #a0751a,

            0px 2px 0 0.1px #8b6914,
            0px 4px 0 0.1px #a0751a,
            0px 6px 0 0.1px #8b6914,
            0px 8px 0 0.1px #a0751a;
    }

    /* Further adjust heights for small mobile */
    .wheat-stalk[data-index="1"] { height: 42px; }
    .wheat-stalk[data-index="2"] { height: 48px; }
    .wheat-stalk[data-index="3"] { height: 40px; }
    .wheat-stalk[data-index="4"] { height: 50px; }
    .wheat-stalk[data-index="5"] { height: 44px; }
    .wheat-stalk[data-index="6"] { height: 46px; }
    .wheat-stalk[data-index="7"] { height: 49px; }
    .wheat-stalk[data-index="8"] { height: 41px; }
    .wheat-stalk[data-index="9"] { height: 47px; }
    .wheat-stalk[data-index="10"] { height: 43px; }
    .wheat-stalk[data-index="11"] { height: 46px; }
    .wheat-stalk[data-index="12"] { height: 42px; }
    .wheat-stalk[data-index="13"] { height: 51px; }
    .wheat-stalk[data-index="14"] { height: 41px; }
    .wheat-stalk[data-index="15"] { height: 48px; }
    .wheat-stalk[data-index="16"] { height: 45px; }
    .wheat-stalk[data-index="17"] { height: 43px; }
    .wheat-stalk[data-index="18"] { height: 49px; }
    .wheat-stalk[data-index="19"] { height: 46px; }
}
